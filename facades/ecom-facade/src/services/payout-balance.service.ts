/* eslint-disable @typescript-eslint/naming-convention */
import {service} from '@loopback/core';
import {HttpErrors} from '@loopback/rest';
import {
  PayoutBalance,
  ForwardShippingRate,
  Shipping,
} from '../models/ecom-service';
import {Shipyaari, ShipRocket} from '../services';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {
  ShipyaariServiceabilityRequest,
  ShipRocketCourierServiceabilityRequest,
} from '../interfaces';

export interface ZoneMappingResult {
  ecomZone: 'LOCAL' | 'ZONAL' | 'NATIONAL';
  courierZone: string;
  chargeAmount: number;
  weightSlab: string;
}

export class PayoutBalanceService {
  constructor(
    @restService(PayoutBalance)
    public payoutBalanceProxy: ModifiedRestService<PayoutBalance>,
    @restService(ForwardShippingRate)
    public forwardShippingRateProxy: ModifiedRestService<ForwardShippingRate>,
    @restService(Shipping)
    public shippingProxy: ModifiedRestService<Shipping>,
    @service('Shipyaari')
    public shipyaariService: Shipyaari,
    @service('ShipRocket')
    public shipRocketService: ShipRocket,
  ) {}

  /**
   * Calculate and create payout balance entry for a shipping record
   */
  async calculateAndCreatePayoutBalance(
    shippingId: string,
    pickupPincode: string,
    deliveryPincode: string,
    weight: number = 1000, // Default weight in grams
    invoiceValue: number = 500, // Default invoice value
  ): Promise<PayoutBalance> {
    // Get shipping record
    const shipping = await this.shippingProxy.findById(shippingId);
    if (!shipping) {
      throw new HttpErrors.NotFound('Shipping record not found');
    }

    // Check if shipping partner ID is set (108 for Shipyaari, 105 for Shiprocket)
    if (
      !shipping.shippingPartnerId ||
      ![105, 108].includes(shipping.shippingPartnerId)
    ) {
      throw new HttpErrors.BadRequest('Invalid or missing shipping partner ID');
    }

    // Calculate zone mapping and charge
    const zoneMappingResult = await this.calculateZoneMapping(
      shipping.shippingPartnerId,
      pickupPincode,
      deliveryPincode,
      weight,
      invoiceValue,
    );

    // Create payout balance entry
    const payoutBalance = new PayoutBalance({
      sellerId: shipping.sellerId,
      shippingId: shippingId,
      chargeAmount: zoneMappingResult.chargeAmount,
      zoneType: zoneMappingResult.ecomZone,
      courierPartnerId: shipping.shippingPartnerId,
      courierZone: zoneMappingResult.courierZone,
      weightSlab: zoneMappingResult.weightSlab,
      pickupPincode: pickupPincode,
      deliveryPincode: deliveryPincode,
    });

    return this.payoutBalanceProxy.create(payoutBalance);
  }

  /**
   * Calculate zone mapping and shipping charge
   */
  private async calculateZoneMapping(
    courierPartnerId: number,
    pickupPincode: string,
    deliveryPincode: string,
    weight: number,
    invoiceValue: number,
  ): Promise<ZoneMappingResult> {
    let courierZone: string;
    let ecomZone: 'LOCAL' | 'ZONAL' | 'NATIONAL';

    if (courierPartnerId === 108) {
      // Shipyaari
      const shipyaariResult = await this.getShipyaariZone(
        pickupPincode,
        deliveryPincode,
        weight,
        invoiceValue,
      );
      courierZone = shipyaariResult.zoneName;
      ecomZone = this.mapShipyaariZoneToEcomZone(shipyaariResult.zoneName);
    } else if (courierPartnerId === 105) {
      // Shiprocket
      const shiprocketResult = await this.getShiprocketZone(
        pickupPincode,
        deliveryPincode,
        weight,
        invoiceValue,
      );
      courierZone = shiprocketResult.zone;
      ecomZone = this.mapShiprocketZoneToEcomZone(shiprocketResult.zone);
    } else {
      throw new HttpErrors.BadRequest('Unsupported courier partner');
    }

    // Get shipping rate from forward_shipping_rates table
    const {chargeAmount, weightSlab} = await this.getShippingRate(
      ecomZone,
      weight,
    );

    return {
      ecomZone,
      courierZone,
      chargeAmount,
      weightSlab,
    };
  }

  /**
   * Get zone information from Shipyaari API
   */
  private async getShipyaariZone(
    pickupPincode: string,
    deliveryPincode: string,
    weight: number,
    invoiceValue: number,
  ): Promise<{zoneName: string}> {
    // Login to get token
    const tokenResponse = await this.shipyaariService.getToken(
      process.env.SHIPYAARI_EMAIL ?? '',
      process.env.SHIPYAARI_PASSWORD ?? '',
    );

    if (!tokenResponse.success || !tokenResponse.data?.[0]?.token) {
      throw new HttpErrors.BadRequest('Failed to authenticate with Shipyaari');
    }

    const token = tokenResponse.data[0].token;

    // Check serviceability
    const serviceabilityRequest: ShipyaariServiceabilityRequest = {
      pickupPincode: parseInt(pickupPincode),
      deliveryPincode: parseInt(deliveryPincode),
      invoiceValue,
      paymentMode: 'PREPAID',
      weight,
      orderType: 'B2C',
      dimension: {
        length: 10,
        width: 10,
        height: 10,
      },
    };

    const serviceabilityResponse =
      await this.shipyaariService.checkServiceability(
        serviceabilityRequest,
        token,
      );

    if (!serviceabilityResponse.success || !serviceabilityResponse.data?.[0]) {
      throw new HttpErrors.BadRequest(
        'Failed to get zone information from Shipyaari',
      );
    }

    return {
      zoneName: serviceabilityResponse.data[0].zoneName,
    };
  }

  /**
   * Get zone information from Shiprocket API
   */
  private async getShiprocketZone(
    pickupPincode: string,
    deliveryPincode: string,
    weight: number,
    invoiceValue: number,
  ): Promise<{zone: string}> {
    // Login to get token
    const tokenResponse = await this.shipRocketService.getToken(
      process.env.SHIPROCKET_EMAIL ?? '',
      process.env.SHIPROCKET_PASSWORD ?? '',
    );

    if (!tokenResponse.token) {
      throw new HttpErrors.BadRequest('Failed to authenticate with Shiprocket');
    }

    // Check serviceability
    const serviceabilityRequest: ShipRocketCourierServiceabilityRequest = {
      pickup_postcode: parseInt(pickupPincode),
      delivery_postcode: parseInt(deliveryPincode),
      weight: weight.toString(),
      declared_value: invoiceValue,
      cod: false,
    };

    const serviceabilityResponse =
      await this.shipRocketService.checkServiceability(
        tokenResponse.token,
        serviceabilityRequest,
      );

    if (!serviceabilityResponse.data?.available_courier_companies?.[0]) {
      throw new HttpErrors.BadRequest(
        'Failed to get zone information from Shiprocket',
      );
    }

    return {
      zone: serviceabilityResponse.data.available_courier_companies[0].zone,
    };
  }

  /**
   * Map Shipyaari zone to EcomDukes zone
   */
  private mapShipyaariZoneToEcomZone(
    shipyaariZone: string,
  ): 'LOCAL' | 'ZONAL' | 'NATIONAL' {
    const zoneMapping: Record<string, 'LOCAL' | 'ZONAL' | 'NATIONAL'> = {
      'Zone 1': 'LOCAL',
      'Zone 2': 'ZONAL',
      'Zone 3': 'NATIONAL',
      'Zone 4': 'NATIONAL',
      'Zone 5': 'NATIONAL',
    };

    return zoneMapping[shipyaariZone] || 'NATIONAL';
  }

  /**
   * Map Shiprocket zone to EcomDukes zone
   */
  private mapShiprocketZoneToEcomZone(
    shiprocketZone: string,
  ): 'LOCAL' | 'ZONAL' | 'NATIONAL' {
    const zoneMapping: Record<string, 'LOCAL' | 'ZONAL' | 'NATIONAL'> = {
      z_a: 'LOCAL',
      z_b: 'ZONAL',
      z_c: 'NATIONAL',
      z_d: 'NATIONAL',
      z_e: 'NATIONAL',
    };

    return zoneMapping[shiprocketZone] || 'NATIONAL';
  }

  /**
   * Get shipping rate from forward_shipping_rates table based on zone and weight
   */
  private async getShippingRate(
    zoneType: 'LOCAL' | 'ZONAL' | 'NATIONAL',
    weight: number,
  ): Promise<{chargeAmount: number; weightSlab: string}> {
    // Get all forward shipping rates
    const rates = await this.forwardShippingRateProxy.find({
      order: ['weightSlab ASC'],
    });

    if (!rates.length) {
      throw new HttpErrors.NotFound('No forward shipping rates found');
    }

    // Find appropriate weight slab (simplified logic - you may need to enhance this)
    let selectedRate = rates[0]; // Default to first rate

    // Simple weight-based selection (you may need to enhance this logic)
    for (const rate of rates) {
      if (rate.weightSlab.toLowerCase().includes('upto') && weight <= 500) {
        selectedRate = rate;
        break;
      } else if (
        rate.weightSlab.toLowerCase().includes('500') &&
        weight > 500
      ) {
        selectedRate = rate;
        break;
      }
    }

    let chargeAmount: number;
    switch (zoneType) {
      case 'LOCAL':
        chargeAmount = selectedRate.localRate;
        break;
      case 'ZONAL':
        chargeAmount = selectedRate.zonalRate;
        break;
      case 'NATIONAL':
        chargeAmount = selectedRate.nationalRate;
        break;
      default:
        throw new HttpErrors.BadRequest('Invalid zone type');
    }

    return {
      chargeAmount,
      weightSlab: selectedRate.weightSlab,
    };
  }
}
