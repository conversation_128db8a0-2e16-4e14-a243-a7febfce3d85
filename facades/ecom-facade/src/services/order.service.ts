import {injectable, BindingScope, inject, Getter} from '@loopback/core';

import {
  CashFreeOrderDto,
  CashFreeOrderSplit,
  Customer,
  CustomerDetails,
  IAuthUserWithTenant,
  Order,
  OrderLineItem,
  OrderLineItemWithRelations,
  OrderWithRelations,
  Payment,
  Seller,
  SubscriptionChargeService,
  EcomdukesServiceChargeService,
  Subscription,
  Ecomdukeservice,
  PayoutBalance,
} from '../models';
import {OrderEntity} from 'cashfree-pg';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {
  CustomerProxyType,
  OrderProxyType,
  PaymentProxyType,
  SubscriptionChargeProxyType,
  EcomdukesServiceChargeProxyType,
} from '../datasources/configs';
import {RestBindings, Request} from '@loopback/rest';
import {AuthenticationBindings} from 'loopback4-authentication';
import {
  DEFAULT_CURRENCY,
  BalanceChargeType,
  BalanceChargeResult,
  ConfigurationKey,
} from '@local/core';
import {SubscriptionStatus} from '../constants';
import {FilterExcludingWhere} from '@loopback/repository';
import {CustomerDto} from '../models/ecom-service/dto/customer-dto.model';
import {Configuration} from '../models/ecom-service/configuration.model';

@injectable({scope: BindingScope.TRANSIENT})
export class OrderService {
  private token: string;
  constructor(
    @restService(Payment)
    public paymentProxy: PaymentProxyType,
    @restService(Order)
    public orderProxy: OrderProxyType,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @restService(Customer)
    private readonly customerProxy: CustomerProxyType,
    @restService(OrderLineItem)
    private readonly orderLineItemProxy: ModifiedRestService<OrderLineItem>,
    @restService(Seller)
    private readonly sellerProxy: ModifiedRestService<Seller>,
    @restService(SubscriptionChargeService)
    private readonly subscriptionChargeService: SubscriptionChargeProxyType,
    @restService(EcomdukesServiceChargeService)
    private readonly ecomdukesServiceChargeService: EcomdukesServiceChargeProxyType,
    @restService(Configuration)
    private readonly configurationProxy: ModifiedRestService<Configuration>,
    @restService(Subscription)
    private readonly subscriptionProxy: ModifiedRestService<Subscription>,
    @restService(Ecomdukeservice)
    private readonly ecomdukeserviceProxy: ModifiedRestService<Ecomdukeservice>,
    @restService(PayoutBalance)
    private readonly payoutBalanceProxy: ModifiedRestService<PayoutBalance>,
  ) {
    if (request.headers.authorization) {
      this.token = request.headers.authorization;
    }
  }

  async createOrderInPg(
    order: Order,
    customer: Customer,
  ): Promise<OrderEntity> {
    const currentUser = await this.getCurrentUser();
    const body = new CashFreeOrderDto({
      customerDetails: new CustomerDetails({
        customerEmail: currentUser.email ?? '',
        customerPhone: currentUser.phone?.substring(2) ?? '',
        customerName: `${currentUser?.firstName} ${currentUser.lastName}`,
        customerId: customer.id,
      }),
      orderNote: `Payment for Order #${order.id} - Thank you for shopping with Ecomdukes!`,
      orderAmount: Number(order.payableAmount),
      orderCurrency: DEFAULT_CURRENCY,
      orderId: order.id,
      orderSplits: await this.getSplitInfo(order),
    });
    const pgOrder = await this.paymentProxy.createOrder(body, this.token);
    await this.orderProxy.updateById(order.id, {
      orderReferenceId: pgOrder.cf_order_id,
    });
    return pgOrder;
  }

  async decorateOrderWithExtras(
    order: OrderWithRelations,
    hasCustomer: boolean,
  ): Promise<OrderWithRelations> {
    // 1. Add previewUrl for each item
    if (order.orderLineItems?.length) {
      order.orderLineItems = order.orderLineItems.map(
        (item: OrderLineItemWithRelations) => {
          const productVariant = item.productVariant;
          const featuredAsset = productVariant?.featuredAsset;
          if (featuredAsset?.preview && process.env.CDN_ORIGIN) {
            featuredAsset.previewUrl = `${process.env.CDN_ORIGIN}/${featuredAsset.preview}`;
          }
          return item;
        },
      );
    }

    if (hasCustomer && order.customerId) {
      const customerFilter: FilterExcludingWhere<CustomerDto> = {
        include: [
          {
            relation: 'userTenant',
            scope: {
              include: [
                {
                  relation: 'user',
                  scope: {
                    fields: ['firstName', 'lastName', 'email'],
                  },
                },
              ],
              fields: ['id'],
            },
          },
        ],
        fields: ['id'],
      };

      const customer = await this.customerProxy.findById(
        order.customerId,
        customerFilter,
      );

      order.customer = customer as unknown as Customer;
    }

    return order;
  }

  extractAndRemoveCustomerRelation<T extends object>(
    filter?: FilterExcludingWhere<T>,
  ): {
    cleanedFilter: FilterExcludingWhere<T> | undefined;
    hasCustomer: boolean;
  } {
    if (!filter?.include) return {cleanedFilter: filter, hasCustomer: false};

    let hasCustomer = false;

    const cleanedInclude = filter.include.filter(rel => {
      if (typeof rel === 'string') {
        if (rel === 'customer') {
          hasCustomer = true;
          return false;
        }
        return true;
      }
      if (rel.relation === 'customer') {
        hasCustomer = true;
        return false;
      }
      return true;
    });

    const cleanedFilter = {
      ...filter,
      include: cleanedInclude.length > 0 ? cleanedInclude : undefined,
    };

    return {cleanedFilter, hasCustomer};
  }

  async getSplitInfo(order: Order): Promise<CashFreeOrderSplit[]> {
    const orderItems = await this.orderLineItemProxy.find({
      where: {orderId: order.id},
      fields: ['id', 'productVariantId', 'quantity', 'totalPrice', 'sellerId'],
    });

    // Group items by seller to calculate balance charges per seller
    const sellerItemsMap = new Map<string, OrderLineItem[]>();
    orderItems.forEach(item => {
      if (!sellerItemsMap.has(item.sellerId)) {
        sellerItemsMap.set(item.sellerId, []);
      }
      sellerItemsMap.get(item.sellerId)!.push(item);
    });

    const orderSplits: CashFreeOrderSplit[] = [];
    const [ecomdukesServiceCommissionPercentage] =
      await this.configurationProxy.find({
        where: {key: ConfigurationKey.EcomDukesServiceCommissionPercentage},
        fields: ['value'],
        limit: 1,
      });
    const commissionPercentage = parseFloat(
      ecomdukesServiceCommissionPercentage?.value || '0',
    );
    // Process all sellers in parallel to avoid async loop
    const sellerPromises = Array.from(sellerItemsMap.entries()).map(
      async ([sellerId, items]) => {
        // Calculate total payout for this seller before balance charges

        const totalSellerPayout = items.reduce((sum, item) => {
          const commission = item.totalPrice * commissionPercentage;
          return sum + (item.totalPrice - commission);
        }, 0);
        // Get seller details
        const seller = await this.sellerProxy.findById(sellerId, {
          fields: {id: true, vendorId: true, userTenantId: true},
          include: [],
        });

        const splits: CashFreeOrderSplit[] = [];

        // Calculate balance charges for subscription, service, and payout balance
        const [
          subscriptionBalanceChargeResult,
          serviceBalanceChargeResult,
          payoutBalanceCharges,
        ] = await Promise.all([
          this.calculateBalanceCharge(
            sellerId,
            totalSellerPayout,
            BalanceChargeType.SUBSCRIPTION,
          ),
          this.calculateBalanceCharge(
            sellerId,
            totalSellerPayout,
            BalanceChargeType.ECOMDUKES_SERVICE,
          ),
          this.calculatePayoutBalanceCharges(sellerId),
        ]);

        const totalBalanceCharge =
          subscriptionBalanceChargeResult.chargeAmount +
          serviceBalanceChargeResult.chargeAmount +
          payoutBalanceCharges;

        let actualPayoutAmount = totalSellerPayout;
        let actualSubscriptionCharge =
          subscriptionBalanceChargeResult.chargeAmount;
        let actualServiceCharge = serviceBalanceChargeResult.chargeAmount;
        let actualPayoutBalanceCharge = payoutBalanceCharges;

        // Handle case where total balance charges exceed seller payout
        if (totalBalanceCharge > totalSellerPayout) {
          // Prioritize: 1) Subscription charges, 2) Service charges, 3) Payout balance charges
          let remainingPayout = totalSellerPayout;

          // First, allocate to subscription charges
          if (subscriptionBalanceChargeResult.chargeAmount >= remainingPayout) {
            actualSubscriptionCharge = remainingPayout;
            actualServiceCharge = 0;
            actualPayoutBalanceCharge = 0;
            remainingPayout = 0;
          } else {
            actualSubscriptionCharge =
              subscriptionBalanceChargeResult.chargeAmount;
            remainingPayout -= actualSubscriptionCharge;

            // Then, allocate to service charges
            if (serviceBalanceChargeResult.chargeAmount >= remainingPayout) {
              actualServiceCharge = remainingPayout;
              actualPayoutBalanceCharge = 0;
              remainingPayout = 0;
            } else {
              actualServiceCharge = serviceBalanceChargeResult.chargeAmount;
              remainingPayout -= actualServiceCharge;

              // Finally, allocate to payout balance charges
              actualPayoutBalanceCharge = Math.min(
                payoutBalanceCharges,
                remainingPayout,
              );
              remainingPayout -= actualPayoutBalanceCharge;
            }
          }

          actualPayoutAmount = 0;

          // Update outstanding balances for unpaid amounts
          const unpaidSubscriptionAmount =
            subscriptionBalanceChargeResult.chargeAmount -
            actualSubscriptionCharge;
          const unpaidServiceAmount =
            serviceBalanceChargeResult.chargeAmount - actualServiceCharge;
          const unpaidPayoutBalanceAmount =
            payoutBalanceCharges - actualPayoutBalanceCharge;

          // Update outstanding balances in respective services
          const balanceUpdatePromises: Promise<void>[] = [];

          if (unpaidSubscriptionAmount > 0) {
            // Get the actual subscription ID for the seller
            const subscriptionId = await this.getActiveSubscriptionId(sellerId);
            if (subscriptionId) {
              balanceUpdatePromises.push(
                this.subscriptionChargeService.updateOutstandingBalance(
                  sellerId,
                  subscriptionId,
                  unpaidSubscriptionAmount,
                  this.token,
                ),
              );
            }
          }

          if (unpaidServiceAmount > 0) {
            // Get the actual service ID
            const serviceId = await this.getActiveServiceId();
            if (serviceId) {
              balanceUpdatePromises.push(
                this.ecomdukesServiceChargeService.updateOutstandingBalance(
                  sellerId,
                  serviceId,
                  unpaidServiceAmount,
                  this.token,
                ),
              );
            }
          }

          // Note: Payout balance charges don't need outstanding balance updates
          // as they represent actual shipping charges that should be deducted from current payout
          if (unpaidPayoutBalanceAmount > 0) {
            console.log(
              `Unpaid payout balance amount for seller ${sellerId}: ${unpaidPayoutBalanceAmount}`,
            );
          }

          // Execute balance updates in parallel
          if (balanceUpdatePromises.length > 0) {
            await Promise.all(balanceUpdatePromises);
          }
        } else {
          // Total charges are less than or equal to payout
          actualPayoutAmount = totalSellerPayout - totalBalanceCharge;
        }

        // Log payout balance charges for audit purposes
        if (payoutBalanceCharges > 0) {
          console.log(
            `Payout balance charges for seller ${sellerId}: ${payoutBalanceCharges}, actual deducted: ${actualPayoutBalanceCharge}`,
          );

          // Mark processed payout balance entries as paid
          await this.markPayoutBalanceAsProcessed(
            sellerId,
            actualPayoutBalanceCharge,
          );
        }

        // Create split for seller payout (after deducting balance charges)
        if (actualPayoutAmount > 0) {
          splits.push(
            new CashFreeOrderSplit({
              vendorId: seller.vendorId,
              amount: actualPayoutAmount,
              tags: {
                sellerId: sellerId,
                orderId: order.id,
                orderDisplayId: order.orderId,
                type: 'SELLER_PAYOUT',
              },
            }),
          );
        }

        return splits;
      },
    );

    // Wait for all seller processing to complete and flatten results
    const allSellerSplits = await Promise.all(sellerPromises);
    orderSplits.push(...allSellerSplits.flat());

    return orderSplits;
  }

  private async calculateBalanceCharge(
    sellerId: string,
    payoutAmount: number,
    type: BalanceChargeType,
  ): Promise<BalanceChargeResult> {
    switch (type) {
      case BalanceChargeType.SUBSCRIPTION:
        return this.subscriptionChargeService.calculateSubscriptionCharge(
          sellerId,
          payoutAmount,
          this.token,
        );

      case BalanceChargeType.ECOMDUKES_SERVICE:
        return this.ecomdukesServiceChargeService.calculateServiceCharge(
          sellerId,
          payoutAmount,
          this.token,
        );

      default:
        throw new Error(`Unsupported balance charge type: ${type}`);
    }
  }

  private async getActiveSubscriptionId(
    sellerId: string,
  ): Promise<string | null> {
    try {
      const subscriptions = await this.subscriptionProxy.find({
        where: {
          subscriberId: sellerId,
          status: SubscriptionStatus.ACTIVE,
        },
        limit: 1,
      });

      return subscriptions.length > 0 ? subscriptions[0].id! : null;
    } catch (error) {
      console.error('Error fetching active subscription:', error);
      return null;
    }
  }

  /**
   * Calculate total payout balance charges for a seller
   * @param sellerId Seller ID to calculate payout balance for
   * @returns Total amount to be deducted from seller payout for shipping charges
   */
  private async calculatePayoutBalanceCharges(
    sellerId: string,
  ): Promise<number> {
    try {
      // Get all unpaid payout balance entries for this seller
      // Note: We should add a 'paid' or 'processed' field to track which entries have been processed
      const payoutBalances = await this.payoutBalanceProxy.find({
        where: {
          sellerId: sellerId,
          deleted: false,
          // TODO: Add condition to only get unpaid/unprocessed entries
          // For now, we get all entries - this should be enhanced to prevent double charging
        },
      });

      // Sum up all charge amounts
      const totalCharges = payoutBalances.reduce((sum, balance) => {
        return sum + (balance.chargeAmount || 0);
      }, 0);

      return totalCharges;
    } catch (error) {
      console.error('Error calculating payout balance charges:', error);
      return 0; // Return 0 if there's an error to avoid breaking the payout process
    }
  }

  /**
   * Mark payout balance entries as processed after deduction
   * @param sellerId Seller ID
   * @param processedAmount Amount that was actually deducted
   */
  private async markPayoutBalanceAsProcessed(
    sellerId: string,
    processedAmount: number,
  ): Promise<void> {
    try {
      if (processedAmount <= 0) return;

      // Get unpaid payout balance entries for this seller
      const payoutBalances = await this.payoutBalanceProxy.find({
        where: {
          sellerId: sellerId,
          deleted: false,
          // TODO: Add condition to only get unpaid/unprocessed entries
        },
        order: ['createdOn ASC'], // Process oldest entries first
      });

      let remainingAmount = processedAmount;
      const updatePromises: Promise<void>[] = [];

      for (const balance of payoutBalances) {
        if (remainingAmount <= 0) break;

        const chargeAmount = balance.chargeAmount || 0;
        if (chargeAmount <= remainingAmount) {
          // This entry is fully paid
          remainingAmount -= chargeAmount;
          // TODO: Mark this entry as fully processed
          // For now, we could delete it or add a 'processed' flag
          updatePromises.push(
            this.payoutBalanceProxy.updateById(balance.id!, {
              deleted: true, // Temporary solution - mark as deleted when processed
              deletedOn: new Date(),
            }),
          );
        } else {
          // This entry is partially paid - we could split it or handle differently
          // For now, we'll leave it as is and log the partial payment
          console.log(
            `Partial payment for payout balance ${balance.id}: ${remainingAmount} of ${chargeAmount}`,
          );
          break;
        }
      }

      if (updatePromises.length > 0) {
        await Promise.all(updatePromises);
        console.log(
          `Marked ${updatePromises.length} payout balance entries as processed for seller ${sellerId}`,
        );
      }
    } catch (error) {
      console.error('Error marking payout balance as processed:', error);
      // Don't throw error to avoid breaking the payout process
    }
  }

  private async getActiveServiceId(): Promise<string | null> {
    try {
      const services = await this.ecomdukeserviceProxy.find({
        where: {
          isActive: true,
        },
        limit: 1,
      });

      return services.length > 0 ? services[0].id! : null;
    } catch (error) {
      console.error('Error fetching active service:', error);
      return null;
    }
  }
}
